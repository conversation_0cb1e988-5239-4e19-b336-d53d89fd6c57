AI_NAME=AISPeak


##################### 需要配置 start #####################
# 数据库配置，数据会进行存储，这个必须配置
DATABASE_URL='mysql+pymysql://{username}:{password}@{mysql_host}/{database}'

# 语音合成配置 azure
AZURE_KEY=

# 配置AI的支持服务 CHAT_GPT 、 ZHIPU ，二选一，设置完后要进行后面参数配置
AI_SERVER=CHAT_GPT

# CHAT_GPT AI，AI_SERVER=CHAT_GPT 时需要配置
CHAT_GPT_PROXY=
CHAT_GPT_KEY=
CHAT_GPT_MODEL=gpt-3.5-turbo-1106

# ZHIPU AI，AI_SERVER=ZHIPU 时需要配置
ZHIPU_AI_API_KEY=
# glm-3-turbo glm-4
ZHIPU_AI_MODEL=glm-3-turbo
##################### 需要配置 end #####################

# 文件保存路径，需要另行保存目录时再配置
TEMP_SAVE_FILE_PATH=./files

# api访问前缀
API_PREFIX=/api
# token加密
TOKEN_SECRET=qwertyuiopasdfghjklzxcvbnm123456
# 是否输入sql语句
SQL_ECHO=False
# token过期时间
TOKEN_EXPIRE_TIME=43200
