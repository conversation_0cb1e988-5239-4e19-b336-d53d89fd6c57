#!/usr/bin/env python3
"""
为现有班级生成班级码的脚本
"""
import sys
import os
import random
import string

# 添加项目路径到系统路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db.task_entities import Class
from app.config import Config


def generate_class_code(session) -> str:
    """生成唯一的6位班级码"""
    while True:
        # 生成6位随机字母数字组合（大写）
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        # 检查是否已存在
        existing = session.query(Class).filter(
            Class.class_code == code,
            Class.deleted_at.is_(None)
        ).first()
        if not existing:
            return code


def update_class_codes():
    """为所有没有班级码的班级生成班级码"""
    # 创建数据库连接
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        # 查询所有没有班级码的班级
        classes_without_code = session.query(Class).filter(
            Class.class_code.is_(None),
            Class.deleted_at.is_(None)
        ).all()
        
        print(f"找到 {len(classes_without_code)} 个没有班级码的班级")
        
        # 为每个班级生成班级码
        for class_obj in classes_without_code:
            class_code = generate_class_code(session)
            class_obj.class_code = class_code
            print(f"班级 '{class_obj.name}' (ID: {class_obj.id}) 生成班级码: {class_code}")
        
        # 提交更改
        session.commit()
        print(f"\n成功更新 {len(classes_without_code)} 个班级的班级码")
        
    except Exception as e:
        session.rollback()
        print(f"错误: {e}")
        raise
    finally:
        session.close()


if __name__ == "__main__":
    update_class_codes()