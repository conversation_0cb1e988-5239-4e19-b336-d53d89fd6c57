[{"gender": 1, "locale": "af-ZA", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (af-ZA, AdriNeural)", "short_name": "af-ZA-AdriNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "af-ZA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (af-ZA, WillemNeural)", "short_name": "af-ZA-WillemNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "am-ET", "local_name": "መቅደስ", "name": "Microsoft Server Speech Text to Speech Voice (am-ET, MekdesNeural)", "short_name": "am-ET-MekdesNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "am-ET", "local_name": "አምሀ", "name": "Microsoft Server Speech Text to Speech Voice (am-ET, AmehaNeural)", "short_name": "am-ET-AmehaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-AE", "local_name": "فاطمة", "name": "Microsoft Server Speech Text to Speech Voice (ar-AE, FatimaNeural)", "short_name": "ar-AE-FatimaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-AE", "local_name": "حمدان", "name": "Microsoft Server Speech Text to Speech Voice (ar-A<PERSON>, HamdanNeural)", "short_name": "ar-AE-HamdanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-BH", "local_name": "ليلى", "name": "Microsoft Server Speech Text to Speech Voice (ar-BH, LailaNeural)", "short_name": "ar-BH-<PERSON><PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-BH", "local_name": "علي", "name": "Microsoft Server Speech Text to Speech Voice (ar-BH, AliNeural)", "short_name": "ar-BH-AliNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-DZ", "local_name": "أمينة", "name": "Microsoft Server Speech Text to Speech Voice (ar-DZ, AminaNeural)", "short_name": "ar-DZ-AminaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-DZ", "local_name": "إسماعيل", "name": "Microsoft Server Speech Text to Speech Voice (ar-DZ, IsmaelNeural)", "short_name": "ar-DZ-Ismael<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-EG", "local_name": "سلمى", "name": "Microsoft Server Speech Text to Speech Voice (ar-EG, SalmaNeural)", "short_name": "ar-EG-SalmaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-EG", "local_name": "شاكر", "name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, ShakirNeural)", "short_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-IQ", "local_name": "رنا", "name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, RanaNeural)", "short_name": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-IQ", "local_name": "باسل", "name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, BasselNeural)", "short_name": "ar-IQ-BasselNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-JO", "local_name": "سناء", "name": "Microsoft Server Speech Text to Speech Voice (ar-J<PERSON>, SanaNeural)", "short_name": "ar-JO-SanaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-JO", "local_name": "تيم", "name": "Microsoft Server Speech Text to Speech Voice (ar-JO, TaimNeural)", "short_name": "ar-JO-TaimNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-KW", "local_name": "نورا", "name": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, NouraNeural)", "short_name": "ar-KW-NouraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-KW", "local_name": "ف<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ar-K<PERSON>, FahedNeural)", "short_name": "ar-KW-FahedNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-LB", "local_name": "ليلى", "name": "Microsoft Server Speech Text to Speech Voice (ar-LB, LaylaNeural)", "short_name": "ar-LB-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-LB", "local_name": "رامي", "name": "Microsoft Server Speech Text to Speech Voice (ar-LB, RamiNeural)", "short_name": "ar-LB-RamiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-LY", "local_name": "إيمان", "name": "Microsoft Server Speech Text to Speech Voice (ar-L<PERSON>, ImanNeural)", "short_name": "ar-LY-ImanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-LY", "local_name": "<PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (a<PERSON><PERSON><PERSON><PERSON>, OmarNeural)", "short_name": "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-<PERSON>", "local_name": "من<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ar-MA, MounaNeural)", "short_name": "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-<PERSON>", "local_name": "جمال", "name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON>, Jamal<PERSON><PERSON>)", "short_name": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-OM", "local_name": "عائشة", "name": "Microsoft Server Speech Text to Speech Voice (ar-OM, AyshaNeural)", "short_name": "ar-OM-AyshaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-OM", "local_name": "عبدا<PERSON><PERSON>ه", "name": "Microsoft Server Speech Text to Speech Voice (ar<PERSON><PERSON><PERSON>, AbdullahNeural)", "short_name": "ar-O<PERSON><PERSON>Abdullah<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-QA", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ar-QA, AmalNeural)", "short_name": "ar-QA-AmalNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-QA", "local_name": "معاذ", "name": "Microsoft Server Speech Text to Speech Voice (ar-QA, MoazNeural)", "short_name": "ar-QA-MoazNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-SA", "local_name": "زارية", "name": "Microsoft Server Speech Text to Speech Voice (ar-SA, ZariyahNeural)", "short_name": "ar-SA-ZariyahNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-SA", "local_name": "<PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ar-SA, HamedNeural)", "short_name": "ar-SA-HamedNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-SY", "local_name": "أ<PERSON><PERSON>ي", "name": "Microsoft Server Speech Text to Speech Voice (ar-S<PERSON>, AmanyNeural)", "short_name": "ar-SY-AmanyNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-SY", "local_name": "ليث", "name": "Microsoft Server Speech Text to Speech Voice (ar-<PERSON><PERSON>, LaithNeural)", "short_name": "ar-S<PERSON>-<PERSON>thNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-TN", "local_name": "ريم", "name": "Microsoft Server Speech Text to Speech Voice (ar-TN, ReemNeural)", "short_name": "ar-TN-ReemNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-TN", "local_name": "هادي", "name": "Microsoft Server Speech Text to Speech Voice (ar-T<PERSON>, HediNeural)", "short_name": "ar-TN-HediNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ar-YE", "local_name": "مريم", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "short_name": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ar-YE", "local_name": "صالح", "name": "Microsoft Server Speech Text to Speech Voice (ar-Y<PERSON>, SalehNeural)", "short_name": "ar-YE-SalehNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "az-AZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (az-AZ, BanuNeural)", "short_name": "az-AZ-BanuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "az-AZ", "local_name": "Babək", "name": "Microsoft Server Speech Text to Speech Voice (az-AZ, BabekNeural)", "short_name": "az-AZ-BabekNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "bg-BG", "local_name": "Калина", "name": "Microsoft Server Speech Text to Speech Voice (bg-BG, KalinaNeural)", "short_name": "bg-BG-KalinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "bg-BG", "local_name": "Борислав", "name": "Microsoft Server Speech Text to Speech Voice (bg-BG, BorislavNeural)", "short_name": "bg-BG-<PERSON><PERSON>Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "bn-BD", "local_name": "নবনীতা", "name": "Microsoft Server Speech Text to Speech Voice (bn-BD, NabanitaNeural)", "short_name": "bn-BD-NabanitaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "bn-BD", "local_name": "প্রদ<PERSON><PERSON><PERSON>প", "name": "Microsoft Server Speech Text to Speech Voice (bn-BD, PradeepNeural)", "short_name": "bn-BD-<PERSON><PERSON>epNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "bn-IN", "local_name": "তানিশা", "name": "Microsoft Server Speech Text to Speech Voice (bn-IN, TanishaaNeural)", "short_name": "bn-IN-<PERSON><PERSON>aaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "bn-IN", "local_name": "ভাস্কর", "name": "Microsoft Server Speech Text to Speech Voice (bn-IN, BashkarNeural)", "short_name": "bn-IN-BashkarNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "bs-BA", "local_name": "Vesna", "name": "Microsoft Server Speech Text to Speech Voice (bs-BA, VesnaNeural)", "short_name": "bs-BA-<PERSON><PERSON><PERSON>Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "bs-BA", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (bs-BA, GoranNeural)", "short_name": "bs-<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ca-ES", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ca<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>)", "short_name": "ca-ES-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ca-ES", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ca-ES, EnricNeural)", "short_name": "ca-ES-EnricNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ca-ES", "local_name": "Alba", "name": "Microsoft Server Speech Text to Speech Voice (ca-ES, AlbaNeural)", "short_name": "ca-ES-AlbaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "cs-CZ", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (cs-CZ, VlastaNeural)", "short_name": "cs-CZ-VlastaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "cs-CZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (cs-CZ, AntoninNeural)", "short_name": "cs-CZ-<PERSON>inNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "cy-GB", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (cy-GB, NiaNeural)", "short_name": "cy-GB-NiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "cy-GB", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (cy-GB, AledNeural)", "short_name": "cy-GB-AledNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "da-DK", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (da<PERSON><PERSON><PERSON>, ChristelNeural)", "short_name": "da-DK-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "da-DK", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (da-<PERSON><PERSON>, JeppeNeural)", "short_name": "da-D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-AT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, IngridNeural)", "short_name": "de-AT-IngridNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-AT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JonasNeural)", "short_name": "de-AT-JonasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-CH", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-CH, LeniNeural)", "short_name": "de-CH-LeniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-CH", "local_name": "Jan", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, JanNeural)", "short_name": "de-CH-Jan<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "short_name": "de-DE-Katja<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ConradNeural)", "short_name": "de-DE-ConradNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful"]}, {"gender": 1, "locale": "de-DE", "local_name": "Amala", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, AmalaNeural)", "short_name": "de-DE-AmalaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, BerndNeural)", "short_name": "de-DE-BerndNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, ChristophNeural)", "short_name": "de-DE-ChristophNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, ElkeNeural)", "short_name": "de-DE-ElkeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, GiselaNeural)", "short_name": "de-DE-GiselaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, KasperNeural)", "short_name": "de-DE-KasperNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, <PERSON>ian<PERSON>al)", "short_name": "de-DE-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "Klarissa", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, KlarissaNeural)", "short_name": "de-DE-KlarissaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, KlausNeural)", "short_name": "de-DE-KlausN<PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON>, LouisaNeural)", "short_name": "de-DE-LouisaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, MajaNeural)", "short_name": "de-DE-Maj<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, RalfNeural)", "short_name": "de-DE-RalfNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "de-DE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (de-DE, TanjaNeural)", "short_name": "de-DE-TanjaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "el-GR", "local_name": "Αθηνά", "name": "Microsoft Server Speech Text to Speech Voice (el-GR, AthinaNeural)", "short_name": "el-GR-AthinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "el-GR", "local_name": "Νέστορας", "name": "Microsoft Server Speech Text to Speech Voice (el-GR, NestorasNeural)", "short_name": "el-GR-NestorasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, WilliamNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, AnnetteNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, CarlyNeural)", "short_name": "en-AU-CarlyN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, DarrenNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, DuncanNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, ElsieNeural)", "short_name": "en-AU-ElsieN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, FreyaNeural)", "short_name": "en-AU-Freya<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, JoanneNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-AU, KenNeural)", "short_name": "en-AU-KenN<PERSON>al", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-AU, KimNeural)", "short_name": "en-AU-Kim<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, NeilNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-AU, TimNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-AU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, TinaNeural)", "short_name": "en-AU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-CA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-CA, ClaraNeural)", "short_name": "en-CA-ClaraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-CA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, LiamNeural)", "short_name": "en-CA-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, SoniaN<PERSON>al)", "short_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "sad"]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, RyanNeural)", "short_name": "en-GB-RyanN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "chat"]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "short_name": "en-GB-LibbyNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, AbbiNeural)", "short_name": "en-GB-AbbiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, AlfieNeural)", "short_name": "en-GB-AlfieNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, BellaNeural)", "short_name": "en-GB-BellaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, ElliotNeural)", "short_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, EthanNeural)", "short_name": "en-GB-EthanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>llie", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, HollieNeural)", "short_name": "en-GB-HollieNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, <PERSON><PERSON>)", "short_name": "en-GB-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, NoahNeural)", "short_name": "en-GB-NoahNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, OliverNeural)", "short_name": "en-GB-OliverNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, OliviaNeural)", "short_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, ThomasNeural)", "short_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-GB", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-GB, MiaNeural)", "short_name": "en-GB-MiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-HK", "local_name": "Yan", "name": "Microsoft Server Speech Text to Speech Voice (en-HK, YanNeural)", "short_name": "en-HK-YanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-HK", "local_name": "Sam", "name": "Microsoft Server Speech Text to Speech Voice (en-HK, SamNeural)", "short_name": "en-HK-SamNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-IE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, EmilyN<PERSON><PERSON>)", "short_name": "en-IE-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-IE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, ConnorNeural)", "short_name": "en-IE-Connor<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-IN", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-IN, NeerjaNeural)", "short_name": "en-IN-NeerjaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-IN", "local_name": "Prab<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-IN, PrabhatNeural)", "short_name": "en-IN-PrabhatNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-KE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, AsiliaNeural)", "short_name": "en-KE-AsiliaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-KE", "local_name": "Chilemba", "name": "Microsoft Server Speech Text to Speech Voice (en-KE, ChilembaNeural)", "short_name": "en-KE-ChilembaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-NG", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-NG, EzinneNeural)", "short_name": "en-NG-EzinneNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-NG", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-NG, AbeoNeural)", "short_name": "en-NG-AbeoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-NZ", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MollyNeural)", "short_name": "en-NZ-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-NZ", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-NZ, MitchellNeural)", "short_name": "en-NZ-MitchellNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-PH", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-PH, RosaNeural)", "short_name": "en-PH-RosaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-PH", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, JamesNeural)", "short_name": "en-PH-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-SG", "local_name": "Luna", "name": "Microsoft Server Speech Text to Speech Voice (en-SG, LunaNeural)", "short_name": "en-SG-LunaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-SG", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON><PERSON>, WayneNeural)", "short_name": "en-SG-Wayne<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-TZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-TZ, ImaniNeural)", "short_name": "en-TZ-ImaniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-TZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-TZ, ElimuNeural)", "short_name": "en-TZ-ElimuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, JennyMultilingualNeural)", "short_name": "en-US-JennyMultilingualNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, JennyNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["assistant", "chat", "customerservice", "newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "short_name": "en-US-GuyN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["newscast", "angry", "cheerful", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"]}, {"gender": 1, "locale": "en-US", "local_name": "Aria", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "short_name": "en-US-AriaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["chat", "customerservice", "narration-professional", "newscast-casual", "newscast-formal", "cheerful", "empathetic", "angry", "sad", "excited", "friendly", "terrified", "shouting", "unfriendly", "whispering", "hopeful"]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, DavisNeural)", "short_name": "en-US-DavisNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["chat", "angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 1, "locale": "en-US", "local_name": "Amber", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AmberNeural)", "short_name": "en-US-AmberN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "Ana", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AnaNeural)", "short_name": "en-US-AnaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AshleyNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, BrandonNeural)", "short_name": "en-US-BrandonN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, ChristopherNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "Cora", "name": "Microsoft Server Speech Text to Speech Voice (en-US, CoraNeural)", "short_name": "en-US-CoraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, ElizabethNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, EricNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, JacobNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, JaneNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, JasonNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, MichelleNeural)", "short_name": "en-US-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, MonicaNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "Nancy", "name": "Microsoft Server Speech Text to Speech Voice (en-US, NancyNeural)", "short_name": "en-US-NancyNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, RogerNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, SaraNeural)", "short_name": "en-US-SaraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-US, SteffanNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-<PERSON>, TonyNeural)", "short_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "cheerful", "excited", "friendly", "hopeful", "sad", "shouting", "terrified", "unfriendly", "whispering"]}, {"gender": 2, "locale": "en-US", "local_name": "AIGenerate1", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AIGenerate1Neural)", "short_name": "en-US-AIGenerate1Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "AIGenerate2", "name": "Microsoft Server Speech Text to Speech Voice (en-US, AIGenerate2Neural)", "short_name": "en-US-AIGenerate2Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-US", "local_name": "Blue", "name": "Microsoft Server Speech Text to Speech Voice (en-US, BlueNeural)", "short_name": "en-US-BlueNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "en-ZA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, LeahNeural)", "short_name": "en-ZA-LeahNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "en-ZA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (en-ZA, LukeNeural)", "short_name": "en-ZA-LukeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-AR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-AR, ElenaNeural)", "short_name": "es-AR-ElenaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-AR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-AR, TomasNeural)", "short_name": "es-AR-TomasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-BO", "local_name": "Sofia", "name": "Microsoft Server Speech Text to Speech Voice (es-BO, SofiaNeural)", "short_name": "es-BO-SofiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-BO", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-B<PERSON>, MarceloNeural)", "short_name": "es-BO-MarceloNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-CL", "local_name": "Catalina", "name": "Microsoft Server Speech Text to Speech Voice (es-CL, CatalinaNeural)", "short_name": "es-CL-CatalinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-CL", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-CL, LorenzoNeural)", "short_name": "es-CL-LorenzoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-CO", "local_name": "Salome", "name": "Microsoft Server Speech Text to Speech Voice (es-CO, SalomeNeural)", "short_name": "es-CO-SalomeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-CO", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-CO, GonzaloNeural)", "short_name": "es-CO-GonzaloNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-CR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-CR, MariaNeural)", "short_name": "es-CR-MariaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-CR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-CR, JuanNeural)", "short_name": "es-CR-JuanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-CU", "local_name": "Belkys", "name": "Microsoft Server Speech Text to Speech Voice (es-CU, BelkysNeural)", "short_name": "es-CU-BelkysNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-CU", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-CU, ManuelNeural)", "short_name": "es-CU-ManuelNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-DO", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, Ramona<PERSON>al)", "short_name": "es-DO-RamonaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-DO", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-D<PERSON>, EmilioNeural)", "short_name": "es-DO-EmilioNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-EC", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-EC, AndreaNeural)", "short_name": "es-EC-AndreaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-EC", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-EC, LuisNeural)", "short_name": "es-EC-LuisNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "short_name": "es-ES-Elvira<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, AlvaroNeural)", "short_name": "es-ES-AlvaroNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "Abril", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, AbrilNeural)", "short_name": "es-ES-AbrilNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "Arna<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, ArnauNeural)", "short_name": "es-ES-ArnauNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "Dar<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, DarioNeural)", "short_name": "es-ES-DarioNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, EliasNeural)", "short_name": "es-ES-EliasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "Estrella", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, EstrellaNeural)", "short_name": "es-ES-EstrellaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, IreneNeural)", "short_name": "es-ES-IreneNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, LaiaNeural)", "short_name": "es-ES-LaiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "Lia", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, LiaNeural)", "short_name": "es-ES-LiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "<PERSON>l", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, NilNeural)", "short_name": "es-ES-NilNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, SaulNeural)", "short_name": "es-ES-SaulNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-ES", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, TeoNeural)", "short_name": "es-ES-TeoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "Triana", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, TrianaNeural)", "short_name": "es-ES-TrianaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-ES", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-ES, VeraNeural)", "short_name": "es-ES-VeraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-GQ", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-G<PERSON>, TeresaNeural)", "short_name": "es-GQ-Teresa<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-GQ", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-GQ, JavierNeural)", "short_name": "es-GQ-JavierNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-GT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-GT, MartaNeural)", "short_name": "es-GT-MartaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-GT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-GT, AndresNeural)", "short_name": "es-GT-AndresNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-HN", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-HN, KarlaNeural)", "short_name": "es-HN-Karla<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-HN", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-HN, CarlosNeural)", "short_name": "es-HN-CarlosNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "Dal<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "short_name": "es-MX-DaliaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, JorgeNeural)", "short_name": "es-MX-JorgeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "chat"]}, {"gender": 1, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, BeatrizNeural)", "short_name": "es-MX-BeatrizNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "Candela", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, CandelaNeural)", "short_name": "es-MX-CandelaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, CarlotaNeural)", "short_name": "es-MX-CarlotaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, CecilioNeural)", "short_name": "es-MX-CecilioNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, GerardoNeural)", "short_name": "es-MX-Gerardo<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "Larissa", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, LarissaNeural)", "short_name": "es-MX-LarissaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, LibertoNeural)", "short_name": "es-MX-Liberto<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, LucianoNeural)", "short_name": "es-MX-LucianoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "Marina", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, MarinaNeural)", "short_name": "es-MX-MarinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, NuriaNeural)", "short_name": "es-MX-NuriaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "Pelayo", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, PelayoNeural)", "short_name": "es-MX-PelayoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-MX", "local_name": "Renata", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, RenataNeural)", "short_name": "es-MX-RenataNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-MX", "local_name": "<PERSON>go", "name": "Microsoft Server Speech Text to Speech Voice (es-MX, YagoNeural)", "short_name": "es-MX-YagoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-NI", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-NI, YolandaNeural)", "short_name": "es-NI-Yo<PERSON>aNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-NI", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-NI, FedericoNeural)", "short_name": "es-NI-FedericoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-PA", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PA, MargaritaNeural)", "short_name": "es-PA-MargaritaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-PA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PA, RobertoNeural)", "short_name": "es-PA-RobertoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-PE", "local_name": "Camila", "name": "Microsoft Server Speech Text to Speech Voice (es-PE, CamilaNeural)", "short_name": "es-PE-CamilaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-PE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PE, AlexNeural)", "short_name": "es-PE-AlexNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-PR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-<PERSON>, <PERSON>a<PERSON>)", "short_name": "es-PR-Karin<PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-PR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PR, VictorNeural)", "short_name": "es-PR-VictorNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-PY", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PY, TaniaNeural)", "short_name": "es-PY-TaniaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-PY", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-PY, MarioNeural)", "short_name": "es-PY-<PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-SV", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-SV, LorenaNeural)", "short_name": "es-SV-LorenaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-SV", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-SV, RodrigoNeural)", "short_name": "es-SV-RodrigoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-US", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-US, PalomaNeural)", "short_name": "es-US-PalomaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-US", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-US, AlonsoNeural)", "short_name": "es-US-AlonsoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-UY", "local_name": "Valentina", "name": "Microsoft Server Speech Text to Speech Voice (es-UY, ValentinaNeural)", "short_name": "es-UY-ValentinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-UY", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-UY, MateoNeural)", "short_name": "es-UY-MateoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "es-VE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (es-VE, PaolaNeural)", "short_name": "es-VE-PaolaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "es-VE", "local_name": "Sebastián", "name": "Microsoft Server Speech Text to Speech Voice (es-V<PERSON>, SebastianNeural)", "short_name": "es-VE-SebastianNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "et-EE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (et-EE, AnuNeural)", "short_name": "et-EE-AnuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "et-EE", "local_name": "Kert", "name": "Microsoft Server Speech Text to Speech Voice (et-EE, KertNeural)", "short_name": "et-EE-KertNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "eu-ES", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (eu-ES, AinhoaNeural)", "short_name": "eu-ES-AinhoaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "eu-ES", "local_name": "Ander", "name": "Microsoft Server Speech Text to Speech Voice (eu-ES, AnderNeural)", "short_name": "eu-ES-AnderNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fa-IR", "local_name": "دلارا", "name": "Microsoft Server Speech Text to Speech Voice (fa-IR, DilaraNeural)", "short_name": "fa-IR-DilaraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fa-IR", "local_name": "فرید", "name": "Microsoft Server Speech Text to Speech Voice (fa-IR, FaridNeural)", "short_name": "fa-IR-FaridNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fi-FI", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fi-FI, SelmaNeural)", "short_name": "fi-FI-SelmaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fi-FI", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fi-FI, HarriNeural)", "short_name": "fi-FI-HarriNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fi-FI", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fi-FI, NooraNeural)", "short_name": "fi-FI-NooraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fil-PH", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fil-PH, BlessicaNeural)", "short_name": "fil-PH-BlessicaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fil-PH", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fil-PH, AngeloNeural)", "short_name": "fil-PH-AngeloNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-BE", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, CharlineNeural)", "short_name": "fr-BE-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-BE", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr<PERSON><PERSON><PERSON>, GerardN<PERSON>al)", "short_name": "fr-B<PERSON>-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-CA", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "short_name": "fr-CA-<PERSON><PERSON>vie<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-CA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON>, JeanNeural)", "short_name": "fr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-CA", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-CA, AntoineNeural)", "short_name": "fr-CA-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-CH", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-CH, ArianeNeural)", "short_name": "fr-CH-ArianeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-CH", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-CH, FabriceNeural)", "short_name": "fr-CH-FabriceNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "short_name": "fr-FR-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "sad"]}, {"gender": 2, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, HenriNeural)", "short_name": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "sad"]}, {"gender": 2, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, AlainN<PERSON>al)", "short_name": "fr-FR-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, BrigitteNeural)", "short_name": "fr-FR-B<PERSON>itteNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "Celeste", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, CelesteNeural)", "short_name": "fr-FR-CelesteNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, ClaudeN<PERSON>al)", "short_name": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, <PERSON>ie<PERSON>al)", "short_name": "fr-FR-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, EloiseNeural)", "short_name": "fr-FR-EloiseNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JacquelineNeural)", "short_name": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, JeromeN<PERSON>)", "short_name": "fr-FR-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, JosephineNeural)", "short_name": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-FR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, MauriceN<PERSON>)", "short_name": "fr-FR-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "fr-FR", "local_name": "Yves", "name": "Microsoft Server Speech Text to Speech Voice (fr-F<PERSON>, YvesNeural)", "short_name": "fr-FR-YvesNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "fr-FR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (fr-FR, YvetteNeural)", "short_name": "fr-FR-YvetteNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ga-IE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ga-IE, OrlaNeural)", "short_name": "ga-IE-OrlaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ga-IE", "local_name": "Colm", "name": "Microsoft Server Speech Text to Speech Voice (ga-IE, ColmNeural)", "short_name": "ga-IE-ColmNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "gl-ES", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (gl-ES, SabelaNeural)", "short_name": "gl-ES-SabelaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "gl-ES", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (gl-ES, RoiNeural)", "short_name": "gl-ES-RoiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "gu-IN", "local_name": "ધ્વની", "name": "Microsoft Server Speech Text to Speech Voice (gu-IN, DhwaniNeural)", "short_name": "gu-IN-DhwaniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "gu-IN", "local_name": "નિરંજન", "name": "Microsoft Server Speech Text to Speech Voice (gu-IN, NiranjanNeural)", "short_name": "gu-IN-NiranjanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "he-IL", "local_name": "הילה", "name": "Microsoft Server Speech Text to Speech Voice (he-IL, HilaNeural)", "short_name": "he-IL-HilaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "he-IL", "local_name": "א<PERSON><PERSON>י", "name": "Microsoft Server Speech Text to Speech Voice (he-IL, AvriNeural)", "short_name": "he-IL-AvriNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "hi-IN", "local_name": "स्वरा", "name": "Microsoft Server Speech Text to Speech Voice (hi-IN, SwaraNeural)", "short_name": "hi-IN-SwaraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "hi-IN", "local_name": "<PERSON><PERSON><PERSON>र", "name": "Microsoft Server Speech Text to Speech Voice (hi-<PERSON>, <PERSON><PERSON><PERSON>)", "short_name": "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "hr-HR", "local_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (hr-HR, GabrijelaNeural)", "short_name": "hr-HR-<PERSON><PERSON><PERSON><PERSON>laNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "hr-HR", "local_name": "<PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (hr-HR, SreckoNeural)", "short_name": "hr-HR-SreckoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "hu-HU", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (hu-HU, NoemiNeural)", "short_name": "hu-HU-NoemiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "hu-HU", "local_name": "Tamás", "name": "Microsoft Server Speech Text to Speech Voice (hu-HU, TamasNeural)", "short_name": "hu-HU-TamasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "hy-AM", "local_name": "Անահիտ", "name": "Microsoft Server Speech Text to Speech Voice (hy-AM, AnahitNeural)", "short_name": "hy-AM-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "hy-AM", "local_name": "Հայկ", "name": "Microsoft Server Speech Text to Speech Voice (hy-AM, HaykNeural)", "short_name": "hy-AM-HaykNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "id-ID", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (id-ID, GadisNeural)", "short_name": "id-ID-GadisNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "id-ID", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (id-ID, ArdiNeural)", "short_name": "id-ID-ArdiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "is-IS", "local_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (is-IS, GudrunNeural)", "short_name": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "is-IS", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (is-IS, GunnarNeural)", "short_name": "is-IS-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "short_name": "it-IT-ElsaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "Isabella", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, IsabellaNeural)", "short_name": "it-IT-IsabellaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["cheerful", "chat"]}, {"gender": 2, "locale": "it-IT", "local_name": "Diego", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, DiegoNeural)", "short_name": "it-IT-DiegoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, BenignoNeural)", "short_name": "it-IT-BenignoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "Calimero", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, CalimeroNeural)", "short_name": "it-IT-CalimeroNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, CataldoNeural)", "short_name": "it-IT-Cat<PERSON>Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, FabiolaNeural)", "short_name": "it-IT-FabiolaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, FiammaNeural)", "short_name": "it-IT-FiammaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, GianniNeural)", "short_name": "it-IT-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "Imelda", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, ImeldaNeural)", "short_name": "it-IT-ImeldaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, IrmaNeural)", "short_name": "it-IT-IrmaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, LisandroNeural)", "short_name": "it-IT-LisandroNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, PalmiraNeural)", "short_name": "it-IT-PalmiraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "it-IT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, PierinaNeural)", "short_name": "it-IT-<PERSON><PERSON>Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "it-IT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (it-IT, RinaldoNeural)", "short_name": "it-IT-RinaldoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ja-<PERSON>", "local_name": "七海", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["chat", "customerservice", "cheerful"]}, {"gender": 2, "locale": "ja-<PERSON>", "local_name": "圭太", "name": "Microsoft Server Speech Text to Speech Voice (ja<PERSON><PERSON>, KeitaNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ja-<PERSON>", "local_name": "碧衣", "name": "Microsoft Server Speech Text to Speech Voice (ja-JP, AoiNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ja-<PERSON>", "local_name": "大智", "name": "Microsoft Server Speech Text to Speech Voice (j<PERSON><PERSON><PERSON>, DaichiNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ja-<PERSON>", "local_name": "真夕", "name": "Microsoft Server Speech Text to Speech Voice (j<PERSON><PERSON><PERSON>, MayuNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ja-<PERSON>", "local_name": "直紀", "name": "Microsoft Server Speech Text to Speech Voice (ja-<PERSON>, NaokiNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ja-<PERSON>", "local_name": "志織", "name": "Microsoft Server Speech Text to Speech Voice (ja-<PERSON>, ShioriNeural)", "short_name": "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "jv-ID", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (jv-ID, SitiNeural)", "short_name": "jv-ID-SitiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "jv-ID", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (jv-ID, DimasNeural)", "short_name": "jv-ID-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ka-GE", "local_name": "ეკა", "name": "Microsoft Server Speech Text to Speech Voice (ka-GE, EkaNeural)", "short_name": "ka-GE-EkaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ka-GE", "local_name": "გიორგი", "name": "Microsoft Server Speech Text to Speech Voice (ka-GE, GiorgiNeural)", "short_name": "ka-GE-GiorgiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "kk-KZ", "local_name": "Айгүл", "name": "Microsoft Server Speech Text to Speech Voice (kk-KZ, AigulNeural)", "short_name": "kk-KZ-AigulNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "kk-KZ", "local_name": "Дәулет", "name": "Microsoft Server Speech Text to Speech Voice (kk-KZ, DauletNeural)", "short_name": "kk-KZ-DauletNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "km-KH", "local_name": "ស្រីមុំ", "name": "Microsoft Server Speech Text to Speech Voice (km-KH, SreymomNeural)", "short_name": "km-KH-SreymomNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "km-KH", "local_name": "ពិសិដ្ឋ", "name": "Microsoft Server Speech Text to Speech Voice (km-KH, PisethNeural)", "short_name": "km-KH-PisethNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "kn-IN", "local_name": "ಸಪ್ನಾ", "name": "Microsoft Server Speech Text to Speech Voice (kn-IN, SapnaNeural)", "short_name": "kn-IN-Sa<PERSON>naNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "kn-IN", "local_name": "ಗಗನ್", "name": "Microsoft Server Speech Text to Speech Voice (kn-<PERSON>, GaganNeural)", "short_name": "kn-IN-GaganNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ko-KR", "local_name": "선히", "name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "short_name": "ko-KR-SunHiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ko-KR", "local_name": "인준", "name": "Microsoft Server Speech Text to Speech Voice (ko-KR, InJoonNeural)", "short_name": "ko-KR-InJoonNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ko-KR", "local_name": "봉진", "name": "Microsoft Server Speech Text to Speech Voice (ko-KR, BongJinNeural)", "short_name": "ko-KR-BongJinNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ko-KR", "local_name": "국민", "name": "Microsoft Server Speech Text to Speech Voice (ko-KR, GookMinNeural)", "short_name": "ko-KR-GookMinNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ko-KR", "local_name": "지민", "name": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, JiMinNeural)", "short_name": "ko-KR-JiMinNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ko-KR", "local_name": "서현", "name": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, SeoHyeonNeural)", "short_name": "ko-KR-SeoHyeonNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ko-KR", "local_name": "순복", "name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SoonBokNeural)", "short_name": "ko-KR-SoonBokNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ko-KR", "local_name": "유진", "name": "Microsoft Server Speech Text to Speech Voice (ko-K<PERSON>, YuJinNeural)", "short_name": "ko-KR-YuJinNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "lo-LA", "local_name": "ແກ້ວມະນີ", "name": "Microsoft Server Speech Text to Speech Voice (lo-LA, KeomanyNeural)", "short_name": "lo-LA-KeomanyNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "lo-LA", "local_name": "ຈັນທະວົງ", "name": "Microsoft Server Speech Text to Speech Voice (lo-LA, ChanthavongNeural)", "short_name": "lo-LA-ChanthavongNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "lt-LT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (lt-LT, OnaNeural)", "short_name": "lt-LT-OnaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "lt-LT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (lt-LT, LeonasNeural)", "short_name": "lt-LT-LeonasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "lv-LV", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (lv-LV, EveritaNeural)", "short_name": "lv-LV-EveritaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "lv-LV", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (lv-LV, NilsNeural)", "short_name": "lv-LV-NilsNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "mk-MK", "local_name": "Марија", "name": "Microsoft Server Speech Text to Speech Voice (mk-MK, MarijaNeural)", "short_name": "mk-MK-MarijaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "mk-MK", "local_name": "Александар", "name": "Microsoft Server Speech Text to Speech Voice (mk-M<PERSON>, AleksandarNeural)", "short_name": "mk-MK-AleksandarNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ml-IN", "local_name": "ശോഭന", "name": "Microsoft Server Speech Text to Speech Voice (ml-IN, SobhanaNeural)", "short_name": "ml-IN-SobhanaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ml-IN", "local_name": "മിഥുൻ", "name": "Microsoft Server Speech Text to Speech Voice (ml-IN, MidhunNeural)", "short_name": "ml-IN-MidhunNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "mn-MN", "local_name": "Есүй", "name": "Microsoft Server Speech Text to Speech Voice (mn-MN, YesuiNeural)", "short_name": "mn-MN-<PERSON>uiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "mn-MN", "local_name": "Батаа", "name": "Microsoft Server Speech Text to Speech Voice (mn-MN, BataaNeural)", "short_name": "mn-MN-BataaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "mr-<PERSON>", "local_name": "आरोही", "name": "Microsoft Server Speech Text to Speech Voice (mr-IN, AarohiNeural)", "short_name": "mr-<PERSON>-<PERSON><PERSON>hiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "mr-<PERSON>", "local_name": "मनो<PERSON>र", "name": "Microsoft Server Speech Text to Speech Voice (mr-<PERSON>, ManoharNeural)", "short_name": "mr-<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ms-MY", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ms<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>al)", "short_name": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ms-MY", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ms-MY, OsmanNeural)", "short_name": "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "mt-MT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (mt-MT, GraceNeural)", "short_name": "mt-MT-GraceNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "mt-MT", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (mt-M<PERSON>, JosephN<PERSON>al)", "short_name": "mt-MT-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "my-MM", "local_name": "နီလာ", "name": "Microsoft Server Speech Text to Speech Voice (my-MM, NilarNeural)", "short_name": "my-MM-<PERSON><PERSON><PERSON><PERSON>al", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "my-MM", "local_name": "သီဟ", "name": "Microsoft Server Speech Text to Speech Voice (my-MM, ThihaNeural)", "short_name": "my-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "nb-NO", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, <PERSON>nilleNeural)", "short_name": "nb-NO-<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "nb-NO", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, FinnNeural)", "short_name": "nb-NO-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "nb-NO", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nb-<PERSON>, IselinNeural)", "short_name": "nb-NO-IselinNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ne-NP", "local_name": "हेमकला", "name": "Microsoft Server Speech Text to Speech Voice (ne-NP, HemkalaNeural)", "short_name": "ne-NP-HemkalaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ne-NP", "local_name": "सागर", "name": "Microsoft Server Speech Text to Speech Voice (ne-NP, SagarNeural)", "short_name": "ne-NP-SagarNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "nl-BE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nl-BE, DenaNeural)", "short_name": "nl-BE-DenaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "nl-BE", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nl-B<PERSON>, ArnaudNeural)", "short_name": "nl-BE-<PERSON><PERSON>udNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "nl-NL", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nl-NL, FennaNeural)", "short_name": "nl-NL-FennaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "nl-NL", "local_name": "Ma<PERSON>n", "name": "Microsoft Server Speech Text to Speech Voice (nl-NL, MaartenNeural)", "short_name": "nl-NL-MaartenNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "nl-NL", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (nl-NL, ColetteNeural)", "short_name": "nl-NL-ColetteNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pl-PL", "local_name": "A<PERSON><PERSON>z<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pl-PL, AgnieszkaNeural)", "short_name": "pl-PL-AgnieszkaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pl-PL", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pl-PL, MarekNeural)", "short_name": "pl-PL-MarekNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pl-PL", "local_name": "Zofia", "name": "Microsoft Server Speech Text to Speech Voice (pl-PL, ZofiaNeural)", "short_name": "pl-PL-ZofiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ps-AF", "local_name": "لطيفه", "name": "Microsoft Server Speech Text to Speech Voice (ps-AF, LatifaNeural)", "short_name": "ps-AF-LatifaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ps-AF", "local_name": " ګل نواز", "name": "Microsoft Server Speech Text to Speech Voice (ps-AF, GulNawazNeural)", "short_name": "ps-AF-GulNawazNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "Francisca", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "short_name": "pt-BR-FranciscaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm"]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, AntonioNeural)", "short_name": "pt-BR-AntonioNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-<PERSON>, <PERSON>N<PERSON><PERSON>)", "short_name": "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, DonatoNeural)", "short_name": "pt-BR-DonatoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "Elza", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, ElzaNeural)", "short_name": "pt-BR-ElzaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FabioNeural)", "short_name": "pt-BR-<PERSON>abio<PERSON><PERSON>al", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "Giovanna", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, GiovannaNeural)", "short_name": "pt-BR-Giovanna<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, HumbertoNeural)", "short_name": "pt-BR-HumbertoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, JulioNeural)", "short_name": "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, LeilaNeural)", "short_name": "pt-BR-Leila<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, LeticiaNeural)", "short_name": "pt-BR-Leticia<PERSON>eural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, ManuelaNeural)", "short_name": "pt-BR-ManuelaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, NicolauNeural)", "short_name": "pt-BR-Nicola<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-BR", "local_name": "Valerio", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, ValerioNeural)", "short_name": "pt-BR-ValerioNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-BR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-BR, YaraNeural)", "short_name": "pt-BR-YaraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-PT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-PT, <PERSON>quel<PERSON>eural)", "short_name": "pt-<PERSON>-<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "pt-PT", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-PT, DuarteNeural)", "short_name": "pt-PT-DuarteNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "pt-PT", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (pt-PT, FernandaNeural)", "short_name": "pt-PT-FernandaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ro-RO", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (ro<PERSON>R<PERSON>, AlinaNeural)", "short_name": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ro-RO", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, EmilNeural)", "short_name": "ro-RO-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ru-RU", "local_name": "Светлана", "name": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, SvetlanaNeural)", "short_name": "ru-RU-<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ru-RU", "local_name": "Д<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (r<PERSON>-<PERSON><PERSON>, DmitryNeural)", "short_name": "ru-RU-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ru-RU", "local_name": "Дар<PERSON>я", "name": "Microsoft Server Speech Text to Speech Voice (ru-R<PERSON>, DariyaNeural)", "short_name": "ru-RU-DariyaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "si-LK", "local_name": "තිළිණි", "name": "Microsoft Server Speech Text to Speech Voice (si-LK, ThiliniNeural)", "short_name": "si-LK-ThiliniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "si-LK", "local_name": "සමීර", "name": "Microsoft Server Speech Text to Speech Voice (si-LK, SameeraNeural)", "short_name": "si-LK-SameeraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sk-SK", "local_name": "Viktória", "name": "Microsoft Server Speech Text to Speech Voice (sk-SK, ViktoriaNeural)", "short_name": "sk-SK-ViktoriaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sk-SK", "local_name": "<PERSON><PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sk-SK, LukasNeural)", "short_name": "sk-SK-LukasNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sl-SI", "local_name": "Petra", "name": "Microsoft Server Speech Text to Speech Voice (sl-SI, PetraNeural)", "short_name": "sl-SI-PetraNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sl-SI", "local_name": "Rok", "name": "Microsoft Server Speech Text to Speech Voice (sl-SI, RokNeural)", "short_name": "sl-SI-RokNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "so-SO", "local_name": "Ubax", "name": "Microsoft Server Speech Text to Speech Voice (so-SO, UbaxNeural)", "short_name": "so-SO-UbaxNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "so-SO", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (so-SO, MuuseNeural)", "short_name": "so-SO-MuuseNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sq-AL", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sq-AL, AnilaNeural)", "short_name": "sq-AL-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sq-AL", "local_name": "Ilir", "name": "Microsoft Server Speech Text to Speech Voice (sq-AL, IlirNeural)", "short_name": "sq-AL-IlirNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sr-Latn-RS", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>tn-RS, NicholasN<PERSON>)", "short_name": "sr-Latn-RS-Nicholas<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sr-Latn-RS", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>tn-RS, SophieNeural)", "short_name": "sr-Latn-RS-SophieNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sr-RS", "local_name": "Софија", "name": "Microsoft Server Speech Text to Speech Voice (sr-RS, SophieNeural)", "short_name": "sr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sr-RS", "local_name": "Никола", "name": "Microsoft Server Speech Text to Speech Voice (sr-<PERSON>, NicholasN<PERSON>al)", "short_name": "sr-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "su-ID", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (su-ID, TutiNeural)", "short_name": "su-ID-TutiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "su-ID", "local_name": "Jajang", "name": "Microsoft Server Speech Text to Speech Voice (su-ID, JajangNeural)", "short_name": "su-ID-JajangNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sv-SE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sv-SE, SofieNeural)", "short_name": "sv-SE-SofieNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sv-SE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sv-SE, MattiasNeural)", "short_name": "sv-SE-Mattias<PERSON>al", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sv-SE", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sv-SE, HilleviNeural)", "short_name": "sv-SE-HilleviNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sw-KE", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, ZuriNeural)", "short_name": "sw-KE-ZuriNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sw-KE", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sw-K<PERSON>, RafikiNeural)", "short_name": "sw-KE-RafikiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "sw-TZ", "local_name": "<PERSON><PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sw-TZ, RehemaNeural)", "short_name": "sw-TZ-RehemaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "sw-TZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (sw-TZ, DaudiNeural)", "short_name": "sw-TZ-DaudiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ta-IN", "local_name": "பல்லவி", "name": "Microsoft Server Speech Text to Speech Voice (ta-IN, PallaviNeural)", "short_name": "ta-IN-PallaviNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ta-IN", "local_name": "வள்ளுவர்", "name": "Microsoft Server Speech Text to Speech Voice (ta-IN, ValluvarNeural)", "short_name": "ta-IN-ValluvarNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ta-LK", "local_name": "சரண்யா", "name": "Microsoft Server Speech Text to Speech Voice (ta-LK, SaranyaNeural)", "short_name": "ta-LK-SaranyaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ta-LK", "local_name": "குமார்", "name": "Microsoft Server Speech Text to Speech Voice (ta<PERSON>L<PERSON>, KumarNeural)", "short_name": "ta-LK-KumarNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ta-MY", "local_name": "கனி", "name": "Microsoft Server Speech Text to Speech Voice (ta-MY, KaniNeural)", "short_name": "ta-MY-KaniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ta-MY", "local_name": "சூர்யா", "name": "Microsoft Server Speech Text to Speech Voice (ta-MY, SuryaNeural)", "short_name": "ta-MY-SuryaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ta-SG", "local_name": "வெண்பா", "name": "Microsoft Server Speech Text to Speech Voice (ta-SG, VenbaNeural)", "short_name": "ta-SG-VenbaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ta-SG", "local_name": "அன்பு", "name": "Microsoft Server Speech Text to Speech Voice (ta-SG, AnbuNeural)", "short_name": "ta-SG-AnbuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "te-IN", "local_name": "శ్రుతి", "name": "Microsoft Server Speech Text to Speech Voice (te-IN, ShrutiNeural)", "short_name": "te-IN-ShrutiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "te-IN", "local_name": "మోహన్", "name": "Microsoft Server Speech Text to Speech Voice (te-<PERSON>, MohanNeural)", "short_name": "te-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "th-TH", "local_name": "เปรมวดี", "name": "Microsoft Server Speech Text to Speech Voice (th-T<PERSON>, PremwadeeNeural)", "short_name": "th-TH-<PERSON><PERSON><PERSON><PERSON>eNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "th-TH", "local_name": "นิวัฒน์", "name": "Microsoft Server Speech Text to Speech Voice (th-TH, NiwatNeural)", "short_name": "th-TH-NiwatNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "th-TH", "local_name": "อัจฉรา", "name": "Microsoft Server Speech Text to Speech Voice (th-TH, AcharaNeural)", "short_name": "th-TH-A<PERSON>raNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "tr-TR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "short_name": "tr-TR-EmelNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "tr-TR", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (tr-TR, AhmetNeural)", "short_name": "tr-TR-AhmetNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "uk-UA", "local_name": "Пол<PERSON>на", "name": "Microsoft Server Speech Text to Speech Voice (uk-UA, PolinaNeural)", "short_name": "uk-UA-PolinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "uk-UA", "local_name": "Остап", "name": "Microsoft Server Speech Text to Speech Voice (uk-UA, OstapNeural)", "short_name": "uk-UA-OstapNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ur-IN", "local_name": "گل", "name": "Microsoft Server Speech Text to Speech Voice (ur-IN, GulNeural)", "short_name": "ur-IN-GulNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ur-IN", "local_name": "سلمان", "name": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON>, SalmanNeural)", "short_name": "ur-IN-SalmanN<PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "ur-PK", "local_name": "عظمیٰ", "name": "Microsoft Server Speech Text to Speech Voice (ur-P<PERSON>, UzmaNeural)", "short_name": "ur-PK-UzmaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "ur-PK", "local_name": "اسد", "name": "Microsoft Server Speech Text to Speech Voice (ur-<PERSON><PERSON>, AsadNeural)", "short_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "uz-UZ", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (uz-UZ, MadinaNeural)", "short_name": "uz-UZ-MadinaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "uz-UZ", "local_name": "Sardor", "name": "Microsoft Server Speech Text to Speech Voice (uz-UZ, SardorNeural)", "short_name": "uz-UZ-SardorNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "vi-VN", "local_name": "Hoài My", "name": "Microsoft Server Speech Text to Speech Voice (vi-VN, HoaiMyNeural)", "short_name": "vi-VN-HoaiMyNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "vi-VN", "local_name": "<PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (vi-VN, NamMinhNeural)", "short_name": "vi-VN-Nam<PERSON>inhNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "wuu-CN", "local_name": "晓彤", "name": "Microsoft Server Speech Text to Speech Voice (wu<PERSON><PERSON><PERSON><PERSON>, XiaotongNeural)", "short_name": "wuu-<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "wuu-CN", "local_name": "云哲", "name": "Microsoft Server Speech Text to Speech Voice (wuu-C<PERSON>, YunzheNeural)", "short_name": "wuu-CN-YunzheNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "yue-CN", "local_name": "晓敏", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, XiaoMinNeural)", "short_name": "yue-CN-Xiao<PERSON>inNeur<PERSON>", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "yue-CN", "local_name": "云松", "name": "Microsoft Server Speech Text to Speech Voice (yue-C<PERSON>, YunSongNeural)", "short_name": "yue-CN-YunSongNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓晓", "name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "short_name": "zh-CN-XiaoxiaoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["assistant", "chat", "customerservice", "newscast", "affectionate", "angry", "calm", "cheerful", "disgruntled", "fearful", "gentle", "lyrical", "sad", "serious", "poetry-reading", "friendly"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云希", "name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunxiNeural)", "short_name": "zh-CN-YunxiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["narration-relaxed", "embarrassed", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "chat", "assistant", "newscast"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云健", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunjianNeural)", "short_name": "zh-CN-YunjianNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["narration-relaxed", "sports-commentary", "sports-commentary-excited"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓伊", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyiNeural)", "short_name": "zh-CN-XiaoyiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "disgruntled", "affectionate", "cheerful", "fearful", "sad", "embarrassed", "serious", "gentle"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云扬", "name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "short_name": "zh-CN-YunyangNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["customerservice", "narration-professional", "newscast-casual"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓辰", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON><PERSON>, XiaochenNeural)", "short_name": "zh-CN-<PERSON><PERSON>N<PERSON>al", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓涵", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>h<PERSON><PERSON><PERSON>, XiaohanNeural)", "short_name": "zh-C<PERSON>-<PERSON><PERSON>Neural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "gentle", "affectionate", "embarrassed"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓梦", "name": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON>, XiaomengNeural)", "short_name": "zh-CN-XiaomengNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["chat"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓墨", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaomoNeural)", "short_name": "zh-CN-XiaomoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "affectionate", "gentle", "envious"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓秋", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoqiuNeural)", "short_name": "zh-CN-XiaoqiuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓睿", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoruiNeural)", "short_name": "zh-CN-XiaoruiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm", "fearful", "angry", "sad"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓双", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoshuangNeural)", "short_name": "zh-CN-XiaoshuangNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["chat"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓萱", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoxuanNeural)", "short_name": "zh-CN-XiaoxuanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "gentle", "depressed"]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓颜", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyanNeural)", "short_name": "zh-CN-<PERSON>yanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓悠", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, XiaoyouNeural)", "short_name": "zh-CN-XiaoyouNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN", "local_name": "晓甄", "name": "Microsoft Server Speech Text to Speech Voice (<PERSON>h<PERSON><PERSON><PERSON>, XiaozhenNeural)", "short_name": "zh-CN-XiaozhenNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "disgruntled", "cheerful", "fearful", "sad", "serious"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云枫", "name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunfengNeural)", "short_name": "zh-CN-YunfengNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["angry", "disgruntled", "cheerful", "fearful", "sad", "serious", "depressed"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云皓", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunhaoNeural)", "short_name": "zh-CN-YunhaoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["advertisement-upbeat"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云夏", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunxiaNeural)", "short_name": "zh-CN-YunxiaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm", "fearful", "cheerful", "angry", "sad"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云野", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunyeNeural)", "short_name": "zh-CN-YunyeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["embarrassed", "calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad"]}, {"gender": 2, "locale": "zh-CN", "local_name": "云泽", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>, YunzeNeural)", "short_name": "zh-CN-YunzeNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": ["calm", "fearful", "cheerful", "disgruntled", "serious", "angry", "sad", "depressed", "documentary-narration"]}, {"gender": 2, "locale": "zh-CN-henan", "local_name": "云登", "name": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON><PERSON><PERSON><PERSON>, YundengNeural)", "short_name": "zh-C<PERSON>-<PERSON><PERSON>-YundengNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN-liaoning", "local_name": "晓北", "name": "Microsoft Server Speech Text to Speech Voice (zh-CN-liaoning, XiaobeiNeural)", "short_name": "zh-CN-liaoning-XiaobeiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-CN-shaanxi", "local_name": "晓妮", "name": "Microsoft Server Speech Text to Speech Voice (zh-<PERSON><PERSON><PERSON><PERSON>, XiaoniNeural)", "short_name": "zh-CN-shaanxi-XiaoniNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "zh-CN-shandong", "local_name": "云翔", "name": "Microsoft Server Speech Text to Speech Voice (zh-C<PERSON>-shand<PERSON>, YunxiangNeural)", "short_name": "zh-CN-shandong-YunxiangNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "zh-CN-sichuan", "local_name": "云希", "name": "Microsoft Server Speech Text to Speech Voice (zh-CN-sichuan, YunxiNeural)", "short_name": "zh-CN-sichuan-YunxiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-HK", "local_name": "曉曼", "name": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuMaanNeural)", "short_name": "zh-HK-HiuMaanNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "zh-HK", "local_name": "雲龍", "name": "Microsoft Server Speech Text to Speech Voice (zh-HK, WanLungNeural)", "short_name": "zh-HK-WanLungNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-HK", "local_name": "曉佳", "name": "Microsoft Server Speech Text to Speech Voice (zh-HK, HiuGaaiNeural)", "short_name": "zh-HK-HiuGaaiNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-TW", "local_name": "曉臻", "name": "Microsoft Server Speech Text to Speech Voice (zh-T<PERSON>, HsiaoChenNeural)", "short_name": "zh-TW-HsiaoChenNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "zh-TW", "local_name": "雲哲", "name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, YunJheNeural)", "short_name": "zh-TW-YunJheNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zh-TW", "local_name": "曉雨", "name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HsiaoYuNeural)", "short_name": "zh-TW-HsiaoYuNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 1, "locale": "zu-ZA", "local_name": "<PERSON><PERSON>", "name": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThandoNeural)", "short_name": "zu-ZA-ThandoNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}, {"gender": 2, "locale": "zu-ZA", "local_name": "Themba", "name": "Microsoft Server Speech Text to Speech Voice (zu-ZA, ThembaNeural)", "short_name": "zu-ZA-ThembaNeural", "voice_type": {"name": "OnlineNeural", "value": 1}, "style_list": [""]}]