<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顶呱呱英语 - 让孩子爱上英语学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .header {
            background: #e5fef1;
            color: #333;
            padding: 20px 0;
            text-align: center;
        }

        .feature-card h3 {
            color: #2fb344;
            margin-bottom: 15px;
        }

        .cta-button {
            display: inline-block;
            background: #2fb344;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.3s;
        }

        .cta-button:hover {
            background: #27963a;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            padding: 20px 0;
        }

        .feature-card {
            background: #fff;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            color: #4A90E2;
            margin-bottom: 15px;
        }

        .cta-button {
            display: inline-block;
            background: #4A90E2;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.3s;
        }

        .cta-button:hover {
            background: #357ABD;
        }

        .testimonials {
            background: #f5f5f5;
            padding: 20px 0 0 0;
        }

        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }

        .qr-code {
            margin: 30px auto;
            text-align: center;
        }
        
        .qr-code img {
            width: 200px;
            height: 200px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .qr-code p {
            margin-top: 15px;
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1>顶呱呱英语</h1>
            <p>科学记忆，快乐学习，让孩子爱上英语</p>
            <div class="qr-code">
                <img src="./qrcode.png" alt="扫码立即体验">
                <p style="color:#666;font-size:14px; margin-bottom: 8px">扫描二维码，立即体验顶呱呱英语</p>
                <a href="https://dingguagua.fun" style="color:#666;font-size:16px">https://dingguagua.fun</a> 
            </div>
        </div>
    </header>

    <main class="container">
           <div class="feature-card">
                <h3>发音测评</h3>
                <p>AI智能评分系统，实时纠正发音错误。通过可视化评分反馈，帮助孩子掌握标准发音。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>

            <div class="feature-card">
                <h3>句子跟读</h3>
                <p>精选教材同步句子，智能语音识别技术。跟读练习提升口语流利度，培养地道英语语感。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>

            <div class="feature-card">
                <h3>AI私教</h3>
                <p>一对一AI助教，个性化学习规划。根据学习情况智能推荐内容，提供专业指导和练习建议。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>
        <section class="features">
            <div class="feature-card">
                <h3>课文点读</h3>
                <p>同步教材课文内容，支持逐句点读和跟读。智能语音合成技术，让孩子随时随地练习标准美音。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>

            <div class="feature-card">
                <h3>艾宾浩斯记忆法</h3>
                <p>遵循科学的记忆曲线，让单词记得更牢固。通过定期复习，有效防止遗忘，提高学习效率。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>

            <div class="feature-card">
                <h3>自然拼读</h3>
                <p>掌握发音规则，建立拼读能力。让孩子做到"见词能读、听音能写"，打好英语学习基础。</p>
                <a href="https://dingguagua.fun" class="cta-button" target="_blank">立即体验</a>
            </div>

         
        </section>

        <section class="testimonials">
            <h2>用户评价</h2>
            <div class="features">
                <div class="feature-card">
                    <p>"孩子使用顶呱呱英语后，英语成绩从70分提升到了92分，学习兴趣也提高了很多！"</p>
                    <p>- 三年级学生家长</p>
                </div>
                <div class="feature-card">
                    <p>"软件的点读功能非常实用，让我的课堂教学变得更加生动有趣。"</p>
                    <p>- 英语老师</p>
                </div>
                <div class="feature-card">
                    <p>"AI发音测评功能太棒了！孩子现在说英语更有信心了，发音也越来越标准。"</p>
                    <p>- 五年级学生家长</p>
                </div>
                <div class="feature-card">
                    <p>"每天坚持使用跟读功能，孩子的口语水平突飞猛进，连外教老师都夸他进步很大。"</p>
                    <p>- 四年级学生家长</p>
                </div>
                <div class="feature-card">
                    <p>"作为英语教师，我特别推荐这款软件。AI助教功能能够及时发现学生的薄弱环节，个性化推荐很到位。"</p>
                    <p>- 初中英语老师</p>
                </div>
                <div class="feature-card">
                    <p>"记单词不再是难事，艾宾浩斯记忆法让复习变得轻松有趣，孩子的词汇量明显提升了。"</p>
                    <p>- 六年级学生家长</p>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>Copyright © 2024 顶呱呱英语 版权所有</p>
        </div>
    </footer>
</body>
</html>