{"status": 1, "msg": "", "data": [{"id": "8641", "category_id": "75", "title": "嗨，新朋友", "sub_title": "<PERSON>, <PERSON>", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2024-06-14/666c0e364bdf7.png", "user_text": "Say Hi and then use one simple question to talk about the theme.", "user_continue_text": "", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和新朋友见面了，互相介绍自己并聊聊自己的年龄等信息。", "info_en": "You've met a new friend, and you're introducing yourselves to each other and talking about information such as your ages.", "is_hot": "0", "copyright": "0", "important_words": "friend\r\nage\r\nyears old\r\nfive\r\nsix\r\nseven\r\nhello\r\nnice to meet you", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "-3", "jump_task": "0", "begin_guide": []}, {"id": "88", "category_id": "75,87", "title": "自我介绍", "sub_title": "在新场合，做个介绍", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-10-17/652e2d966b4cc.png", "user_text": "Hi!", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你需要向新朋友做个简单的自我介绍。你可以分享你的姓名、年龄、性别、国籍等基本信息，也可以谈论你的兴趣爱好或者个人趣事等。", "info_en": "You need to make a brief introduction to your new friend. You can share your basic information such as your name, age, gender, nationality, and also discuss your interests, hobbies, or personal anecdotes.", "is_hot": "1", "copyright": "0", "important_words": "nationality\r\ninterests\r\nhobbies\r\npersonal anecdotes\r\nintroduce\r\nconversation\r\nbrief\r\nnew setting\r\nbasic information\r\nshare", "min_version": "", "max_version": "", "hot_sort": "-300", "sort": "100", "jump_task": "1", "begin_guide": []}, {"id": "117", "category_id": "", "title": "假期安排", "sub_title": "分享假期安排或计划", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-10-17/652e2d11827df.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about holiday.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和朋友在聊近期的假期。你可以向朋友介绍上一次你的假期安排，有哪些印象深刻的事情。也可以聊聊下次假期的安排。", "info_en": "You and your friends are discussing recent holiday. You're talking about how you planned previous holiday, and if there were any memorable experiences or recommendations. You might also talk about plans for your next holiday.", "is_hot": "1", "copyright": "0", "important_words": "discussing\r\nplanned\r\nprevious\r\nmemorable\r\nexperiences\r\nrecommendations\r\ndestination\r\naccommodation\r\nitinerary\r\nbudget\r\ntransportation\r\nactivities\r\nsightseeing\r\nrelaxation\r\nadventure\r\nculture\r\nlanguage\r\nsouvenirs\r\nmemories", "min_version": "", "max_version": "", "hot_sort": "-100", "sort": "1", "jump_task": "0", "begin_guide": []}, {"id": "110", "category_id": "", "title": "点餐与买单", "sub_title": "如何在餐厅点餐和买单", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2024-08-02/66ac7e26998ba.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about restaurant orders.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你来到一家餐馆，你需要完成点餐、结账，点餐的时候可以让服务员给你推荐一些特色菜。", "info_en": "You arrived at a restaurant and need to order and pay for your meal. You can ask the waiter for recommendations on the restaurant's specialties.", "is_hot": "1", "copyright": "0", "important_words": "menu\r\nappetizer\r\nentree\r\ndessert\r\nspecial\r\nsoup\r\nsalad\r\nsandwich\r\nburger\r\nsteak\r\nseafood\r\npasta\r\nvegetarian\r\nvegan\r\ngluten-free\r\nside dish\r\ndressing\r\nsauce\r\nseasoning\r\nspicy\r\nmild\r\nmedium\r\nrare\r\nmedium-rare\r\nwell-done\r\ncooked\r\ngrilled\r\nfried\r\nbaked\r\nsautéed\r\nroasted\r\nbill\r\ncheck\r\ntip\r\ntotal\r\ntax\r\ncredit card\r\ncash\r\nchange\r\nreceipt\r\ngratuity\r\nreservation\r\nhost\r\nserver\r\ntable\r\nbooth\r\nhigh chair\r\nmenu board\r\nspecials board\r\nwater\r\nsoda\r\nwine\r\nbeer\r\ncocktail\r\nmocktail", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "161", "category_id": "75", "title": "看电影", "sub_title": "邀请好友去看电影", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-13/6551e6d725627.png", "user_text": "You should start the conversation with simple greetings. ", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你打算约你的朋友去看电影。你可以询问他对于电影的喜好。确定电影类型后，你需要再与朋友确认一下电影的开场时间。这样可以确保你们的计划顺利进行。", "info_en": "When you plan to invite your friend to watch a movie, you could ask about his movie preferences. After determining the type of movie, you need to confirm the movie's start time with your friend. This way, you can ensure that your plans go smoothly.", "is_hot": "1", "copyright": "0", "important_words": "movie\r\nfilm\r\nplan\r\ninvite\r\npreference\r\ngenre\r\nconfirm\r\nschedule\r\nsmoothly\r\nscene\r\nticket\r\ncinema\r\nseat\r\npopcorn\r\ntrailer\r\nactor\r\nactress\r\ndirector\r\nplot\r\ncharacter\r\nreview\r\nrating\r\nsubtitles\r\ndubbed\r\noriginal\r\nlanguage\r\nonline\r\nwebsite\r\nbooking\r\navailable\r\nsold out\r\nqueue\r\nshowtime\r\nmatinee\r\npremiere\r\nending\r\nanimation\r\nsuspense film\r\nsuspense movie\r\naction film\r\naction movie\r\nadventure film\r\nadventure movie\r\nanimation\r\nanimated film\r\nanimated movie\r\nbiography\r\ncomedy film\r\ncomedy movie\r\ncrime film\r\ncrime movie\r\ndocumentary\r\ndrama\r\nfantasy movie\r\nfantasy film\r\nhistory movie\r\nhistory film\r\nhorror movie\r\nhorror film\r\nmusical movie\r\nmusical film\r\nmystery movie\r\nmystery film\r\nromance movie\r\nromance film\r\nscience fiction\r\nthriller movie\r\nthriller film\r\nwar movie\r\nwar film\r\nwestern movie\r\nwestern film\r\ndisaster movie\r\ndisaster film", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "2", "jump_task": "0", "begin_guide": []}, {"id": "9609", "category_id": "87", "title": "喜欢的宠物", "sub_title": "Favorite Pet", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2024-06-24/66791a7828eac.png", "user_text": "Say Hi and then use one simple question to talk about the theme.", "user_continue_text": "", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和对方在谈论你们喜欢的宠物，你可以分享你喜欢的宠物是什么，为什么喜欢它们。", "info_en": "You are discussing the pets you like. You can share what pets you like and why you like them.", "is_hot": "1", "copyright": "0", "important_words": "", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "1", "jump_task": "0", "begin_guide": []}, {"id": "11818", "category_id": "", "title": "快乐时光", "sub_title": "Happy Hour", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2024-07-05/6687ae5f65659.png", "user_text": "Say Hi and then use one simple question to talk about the theme.", "user_continue_text": "", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和对方正讨论自己喜欢的休闲乐趣活动。", "info_en": "You are discussing your favorite leisure activities.", "is_hot": "1", "copyright": "0", "important_words": "play the violin\r\nguitar\r\nswim\r\ndance\r\ncan\r\ncan't", "min_version": "", "max_version": "", "hot_sort": "-5", "sort": "1", "jump_task": "0", "begin_guide": []}, {"id": "116", "category_id": "", "title": "谈论爱好", "sub_title": "聊聊各自的爱好", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-10-17/652e2d2f5cce6.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about hobbies.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你需要和朋友讨论各自的爱好，爱好是什么，爱好的由来。 ", "info_en": "You and your friend are discussing your hobbies and need to talk about each other's hobbies, why you enjoy them.", "is_hot": "1", "copyright": "0", "important_words": "interest\r\npassion\r\nenjoy\r\nactivity\r\npastime\r\nleisure\r\nrelaxation\r\nentertainment\r\npleasure\r\nfavorite\r\npreference\r\nskill\r\ntalent\r\ncreativity\r\nexpression\r\noutlet\r\npursuit\r\nchallenge\r\nsatisfaction\r\nfulfillment\r\naccomplishment\r\ngoal\r\nachievement", "min_version": "", "max_version": "", "hot_sort": "-2", "sort": "-101", "jump_task": "0", "begin_guide": []}, {"id": "9585", "category_id": "", "title": "喜欢的食物", "sub_title": "Favorite Food", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2024-06-24/6678dce1baa2e.png", "user_text": "Say Hi and then use one simple question to talk about the theme.", "user_continue_text": "", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和对方在谈论你们最喜欢的食物，可以分享你们的食物喜好. ", "info_en": "You and the other person are discussing your favorite foods. You can share your food preferences.", "is_hot": "1", "copyright": "0", "important_words": "food\r\nlike\r\nfavorite\r\nfood\r\napple\r\nbanana\r\npizza\r\nice cream", "min_version": "", "max_version": "", "hot_sort": "-3", "sort": "1", "jump_task": "0", "begin_guide": []}, {"id": "122", "category_id": "74,88", "title": "家庭成员", "sub_title": "介绍你的家庭成员", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-10-17/652e2c8d0d389.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about family members.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "当你第一次认识新朋友或同事时，你们谈到了各自的家庭。你需要向对方介绍一下家庭成员情况。", "info_en": "When you first meet a new friend or colleague, you talk about each other's families. You need to introduce your family members to each other.", "is_hot": "1", "copyright": "1", "important_words": "parents\r\nsiblings\r\nspouse\r\nchildren\r\ngrandparents\r\ncousin\r\ninlaws\r\nstepfamily\r\nblended family\r\nnuclear family\r\nextended family\r\nfamily tree\r\nfamily history\r\nfamily traditions\r\nfamily values\r\nfamily dynamics", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "-100", "jump_task": "0", "begin_guide": []}, {"id": "146", "category_id": "74", "title": "描述你的家人", "sub_title": "家人的外貌和性格", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-02/654360fb7db40.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about family members.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你给你的朋友介绍自己的家庭成员。你可以向对方描述一下家人的外貌和性格特征，也可以谈谈家人的工作等。", "info_en": "You are introducing your family members to your friend. You can describe the appearance and personality traits of your family members, and also talk about their jobs, etc.", "is_hot": "0", "copyright": "0", "important_words": "family\r\nparents\r\nfather\r\nmother\r\ngrandparents\r\ngrandfather\r\ngrandmother\r\nuncle\r\naunt\r\ncousin\r\nsiblings\r\nbrother\r\nsister\r\nappearance\r\npersonality\r\nprofession\r\ntraits\r\ncharacter\r\ntall\r\nshort\r\nkind\r\nhardworking\r\ngenerous\r\nfunny\r\nserious\r\noccupation\r\njob\r\nwork\r\nhobby\r\ninterests\r\nlikes\r\ndislikes\r\nloving\r\ncaring\r\nsupportive\r\nstrict\r\ncheerful\r\nfriendly\r\nintelligent\r\npatient\r\nreliable\r\nresponsible\r\nhobbies\r\nactivities\r\npastime\r\nsports\r\nmusic\r\ncooking\r\nreading\r\nmovies\r\ngames\r\ngardening\r\ntraveling\r\npainting\r\ndancing\r\nfishing\r\nhiking\r\nphotography", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "147", "category_id": "87,88", "title": "分享家庭故事", "sub_title": "趣味故事有多少", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-02/654360dd5ffe9.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about family stories.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和朋友在互相分享家庭故事。你可以向朋友介绍记忆家庭的传统活动，或者讲述一段属于你们家的趣味故事。", "info_en": "You and your friend are sharing family stories with each other. You can introduce your friend to traditional family activities from your memory, or tell a fun story that belongs to your family.", "is_hot": "0", "copyright": "0", "important_words": "family\r\ntradition\r\nstory\r\nshare\r\nmemory\r\nfun\r\nevent\r\nchildhood\r\nrelative\r\ncelebrate\r\nholiday\r\ngathering\r\ncousin\r\ngrandparent\r\nsibling\r\naunt\r\nuncle\r\nnephew\r\nniece\r\nlaughter\r\nmeal\r\nrecipe\r\ngift\r\ngeneration\r\nalbum\r\nphoto\r\npast\r\npresent\r\nfuture\r\nheritage\r\nculture\r\ncustom\r\nreunion\r\nanecdote\r\nhome\r\nupbringing\r\ncherish\r\nnostalgic\r\nreminiscence\r\nkeepsake\r\nheirloom\r\nlegacy\r\nfestive\r\noccasion\r\nbond\r\nroots\r\nsentiment\r\nhousehold\r\nupbringing\r\njoyous", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "159", "category_id": "74,88", "title": "天气情况", "sub_title": "谈谈今日天气", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-13/6551c7a10ef1e.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about weather.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你和朋友闲聊天气时，你可以主动发问，今天的天气如何？同时，分享你的观察。你也可以表达天气对你的影响。", "info_en": "When you're casually discussing the weather with a friend, you could initiate the conversation by asking, 'What's the weather like today?' At the same time, share your observations. You could also express how the weather affects you.", "is_hot": "1", "copyright": "0", "important_words": "sunny\r\ncloudy\r\nrainy\r\nsnowy\r\nwindy\r\nstormy\r\nhumid\r\nhot\r\nwarm\r\ncool\r\ncold\r\nfreezing\r\ntemperature\r\nforecast\r\nclimate\r\nprecipitation\r\nthunder\r\nlightning\r\nhail\r\nsleet\r\nmist\r\nfog\r\ndew\r\nhumidity\r\nbarometer\r\nthermometer\r\nanemometer\r\nwind chill\r\nheat index\r\nUV index\r\nsunrise\r\nsunset\r\ndaylight\r\novercast\r\ndrizzle\r\ndownpour\r\nblizzard\r\nhurricane\r\ntornado\r\nmonsoon\r\ndrought\r\nflood\r\nthaw\r\nicicles\r\npuddles", "min_version": "", "max_version": "", "hot_sort": "1", "sort": "-102", "jump_task": "0", "begin_guide": []}, {"id": "162", "category_id": "87", "title": "去爬山", "sub_title": "爬山活动", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-13/6551e6f36502c.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about climbing mountains.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你向你的朋友提议去爬山。你可以询问他的意愿。以及确定好你们的见面时间和地点。", "info_en": "You propose to your friend to go hiking. You can inquire about his willingness. And determine your meeting time and place.", "is_hot": "0", "copyright": "0", "important_words": "friend\r\npropose\r\nmountain\r\nclimbing\r\nwillingness\r\nconfirm\r\nmeet\r\ntime\r\nplace\r\nschedule\r\nactivity\r\ninterest\r\nplan\r\nagree\r\ndisagree\r\nlocation\r\nmap\r\nroute\r\nbackpack\r\ngear\r\nsnack\r\nweather\r\nforecast\r\nsunny\r\ncloudy\r\nrainy\r\ntemperature\r\nsunscreen\r\ncamera\r\nview\r\nnature\r\nhike\r\ntrail\r\nsummit\r\ntired\r\nenjoy", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "4", "jump_task": "0", "begin_guide": []}, {"id": "165", "category_id": "75", "title": "打车", "sub_title": "如何打到出租车", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-14/6553148b827f3.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about taking a taxi.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你急切地需要前往你的目的地，因此你打算乘坐出租车。你需要明确告诉司机你的目的地，并询问能否在你所需的时间内到达。此外，你还需要向司机询问大概的车费是多少。", "info_en": "You urgently need to get to your destination, so you plan to take a taxi. You need to clearly tell the driver where you're going and ask if you can get there in the time you need. In addition, you also need to ask the driver about the approximate fare.", "is_hot": "0", "copyright": "0", "important_words": "taxi\r\ndestination\r\ndriver\r\narrival\r\nfare\r\nroute\r\ntraffic\r\nrush\r\nschedule\r\nmeter\r\npayment\r\ntip\r\nluggage\r\nseatbelt\r\njourney\r\naddress\r\nestimate\r\ndistance\r\nspeed\r\ndirection\r\nhighway\r\nlocation\r\nnavigation\r\npassenger\r\nrush hour\r\ndetour\r\nintersection\r\nsignal\r\nfast\r\nslow\r\npickup\r\ndrop-off\r\nhurry\r\ndelay\r\nearly\r\nlate\r\nminutes\r\nhours\r\nkilometers\r\nmiles", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "3", "jump_task": "0", "begin_guide": []}, {"id": "167", "category_id": "74", "title": "乘地铁", "sub_title": "地铁出行二三事", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-14/655315df41401.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about taking a subway.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你正在准备乘坐地铁，并正在与地铁站工作人员交流。你需要询问地铁的停靠站情况、票价，以及结合建议，你该为行程选择哪条地铁线路。", "info_en": "You're gearing up to take the subway and are currently interacting with the station staff. You need to inquire about the stations where the subway stops, the cost of the tickets, and get their advice on which subway line would be the best for your journey.", "is_hot": "0", "copyright": "0", "important_words": "subway\nstation\nplatform\nticket\nfare\nroute\nmap\nschedule\ntransfer\ndirection\ndestination\njourney\nline\ntimetable\ndeparture\narrival\nconductor\npassenger\nluggage\nescalator\nelevator\nstaircase\nturnstile\nentrance\nexit\ninformation\nannouncement\ndelay\nrush hour\noff-peak\nsingle ticket\nreturn ticket\nvending machine\ncustomer service\nlost and found\nsafety\ncarriage\nseat\nstanding area\nhandrail\nticket barrier\nmetro card\nvalidation\ninspector\ncrowd\nqueue\ntravel time\nfirst train\nlast train\nfrequency", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "166", "category_id": "74", "title": "坐公交车", "sub_title": "绿色出行", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-14/655314ecb6740.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about taking a bus.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "当你需要乘坐公交车时，你可能需要向司机询问一些重要的信息，例如公交车是否经过你的目的地，以及下一站是哪里。", "info_en": "When you need to take a bus, you might need to ask the driver some important information, such as whether the bus passes your destination, and what the next stop is.", "is_hot": "0", "copyright": "0", "important_words": "bus\ndriver\ndestination\nstop\nticket\nroute\ntimetable\nschedule\nfare\nseat\npassenger\nluggage\nstation\nplatform\nentrance\nexit\nmap\ndirection\njourney\ntransfer\nline\narrival\ndeparture\ndelay\nannouncement\nboarding\nalighting\nsingle ticket\nreturn ticket\ntimetable\nmachine\nservice\ncrowd\nqueue\ntravel time\nfirst bus\nlast bus\nfrequency\nrush hour\noff-peak\nbus card\ncheck\ninspector\nlost and found\nsafety\ncarriage\nstanding area\nhandrail\nticket gate\ninformation", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "172", "category_id": "75,87", "title": "生日庆祝", "sub_title": "帮朋友过生日", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-16/65557377c9829.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about birthday celebration.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你的朋友的生日即将到来，你将询问朋友的礼物喜好，以及派对的举办地点。你想知道朋友是否有特别想要的礼物，或者有没有特别喜欢的庆祝方式和地点，以便能帮助朋友一起筹备这个重要的日子。", "info_en": "Your friend's birthday is coming up, and you plan to ask about their gift preferences and the location for the party. You want to know if there is any special gift they want, or if they have a particular way and place they like to celebrate, so that you can help your friend prepare for this important day.", "is_hot": "0", "copyright": "0", "important_words": "birthday\r\nfriend\r\ngift\r\nparty\r\nfavorite\r\nprepare\r\nspecial\r\ncake\r\ncandle\r\nwish\r\ncard\r\nsurprise\r\nballoon\r\ninvite\r\nguest\r\ncelebrate\r\nmemory\r\npresent\r\nunwrap\r\ndecoration\r\ntheme\r\nschedule\r\narrangement\r\nsuggestion\r\ncheers\r\ndelight\r\npleasure\r\ncelebration\r\nguest list\r\nchampagne\r\ntoast\r\ndress code\r\ncostume\r\nformal\r\ncasual\r\nbudget\r\npreparation\r\npreference", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "-1", "jump_task": "0", "begin_guide": []}, {"id": "177", "category_id": "74,88", "title": "挑选礼物", "sub_title": "如何挑到心仪的礼物", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-17/6556c86a80bed.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about choosing gifts.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你计划为家人送份礼物，但不确定是否适合。于是向朋友寻求建议。你可以根据朋友给出的建议确定最终的礼物，并向朋友表示感谢。", "info_en": "You plan to give a gift to your family but are unsure if it's suitable. You seek advice from a friend. Based on your friend's suggestions, you finalize the gift and express gratitude.", "is_hot": "0", "copyright": "0", "important_words": "gift\r\nsuitable\r\nunsure\r\nadvice\r\ndecide\r\nappreciate\r\nsuggestion\r\nconsider\r\npresent\r\noccasion\r\nonline\r\nstore\r\nbudget\r\naffordable\r\nexpensive\r\ncheap\r\nquality\r\ntaste\r\npreference\r\nstyle\r\nfashion\r\ntrendy\r\nclassic\r\npersonal\r\nunique\r\nthoughtful\r\npleased\r\ndelighted\r\ncelebrate\r\nfestive\r\nbirthday\r\nanniversary\r\nholiday\r\nChristmas\r\nwrap\r\npackage\r\ndelivery\r\norder\r\npayment", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "106", "category_id": "", "title": "餐厅点餐", "sub_title": "如何在餐厅顺利点餐", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-10-17/652e3feedbc82.png", "user_text": "You should start the conversation with simple greetings. Then use one question to ask me about my order.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你来到一家餐馆。你需要让服务员进行特色菜的推荐并完成点餐流程，用餐结束后完成结账。", "info_en": "You arrive at a restaurant. You need to have the waiter recommend the specialty dishes, complete the ordering process, and settle the bill after the meal.", "is_hot": "0", "copyright": "0", "important_words": "recommend\r\nspecialty dishes\r\nordering process\r\nsettle\r\nentree\r\ncheeseburger\r\ntopping\r\nlettuce\r\nburger\r\nfries\r\nsoda\r\ncheck\r\nreservation\r\ntable\r\nseating\r\natmosphere\r\ncuisine\r\ningredients\r\nallergies\r\nvegetarian\r\nglutenfree\r\ndairyfree\r\nspicy\r\nmild\r\ncooked\r\nrare\r\nmediumrare\r\nmedium\r\nwelldone\r\ndressing\r\nside dish\r\nportion size\r\ndrink\r\npayment\r\ncredit card\r\ncash\r\nchange\r\nadd\r\ncoke\r\ncoca cola", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "181", "category_id": "74", "title": "售前咨询", "sub_title": "挑选合适的衣服", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-23/655eec868184a.png", "user_text": "You should start the conversation with simple greetings.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你走进一家服饰店，打算为自己挑选一件新衣服。你向店员询问了各种关于衣服的细节，包括尺码是否合适，颜色是否有其他选择，以及这件衣服的材料是什么。你希望通过这些信息，找到一件完全符合自己喜好的衣服", "info_en": "You walk into a clothing store, intending to pick out a new outfit for yourself. You ask the salesperson various details about the clothes, including whether the size is suitable, whether there are other color options, and what material the clothes are made of. You hope to find the item that perfectly suits your preferences through this information.", "is_hot": "0", "copyright": "0", "important_words": "clothing\r\nstore\r\nsize\r\ncolor\r\nmaterial\r\nfit\r\nstyle\r\nprice\r\ndiscount\r\nbrand\r\nfashion\r\npattern\r\nwardrobe\r\noutfit\r\ndressing room\r\ncashier\r\npurchase\r\npayment\r\nreceipt\r\nexchange\r\nrefund\r\ncustomer service\r\nsalesperson\r\ndesigner\r\ntrend\r\ncollection\r\nshopping cart\r\nonline shopping\r\ndelivery\r\nreturn policy\r\nquality\r\nbargain\r\nclearance\r\nsale\r\ncatalogue\r\nstock\r\nretail\r\nseason\r\nfitting\r\ntailor\r\nfabric\r\ncotton\r\nsilk\r\nwool\r\nsynthetic\r\nleather\r\ndenim\r\npolyester\r\nlinen\r\nvelvet", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "184", "category_id": "74", "title": "买衣试穿", "sub_title": "试衣服交流", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-23/655efc644de35.png", "user_text": "You should start the conversation with simple greetings.", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你走进服装店，寻找心仪的新装。你向店员询问衣服是否有适合自己的尺码，并询问试衣间的位置。试穿后，你可以表达对衣服的满意度。", "info_en": "You enter a clothing store, looking for a desired new outfit. You ask the staff if the clothes have a size suitable for you, and inquire about the location of the fitting room. After trying on, you can express your satisfaction with the clothes.", "is_hot": "0", "copyright": "0", "important_words": "boutique\r\nfitting room\r\ntry on\r\nstyle\r\nfashion\r\noutfit\r\nshirt\r\npants\r\ndress\r\nsweater\r\nskirt\r\njeans\r\ncoat\r\ndiscount\r\nsale\r\ncashier\r\npayment\r\ncomfortable\r\ntight\r\nloose\r\npattern\r\nmaterial\r\ncotton\r\nsilk\r\nwool\r\nsynthetic\r\nleather\r\nperfect\r\nbeautiful\r\nugly\r\ntrendy\r\nold-fashioned\r\nchange\r\ncash\r\ncredit card\r\nattractive\r\ncheckout\r\nwardrobe\r\naccessory\r\nscarf\r\nhat\r\ngloves\r\nsatisfaction\r\ndissatisfaction\r\nrecommend", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "188", "category_id": "74", "title": "预约检查", "sub_title": "如何做预约", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-27/6564511c9c582.png", "user_text": "You should start the conversation with simple greetings. ", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你在医院，与前台交谈，希望预约一次检查。你详细询问最早的可预约时间，对比自己的日程，最终确定并确认了预约时间。", "info_en": "You are in the hospital, talking to the receptionist, hoping to schedule a check-up. You ask in detail about the earliest available appointment time, compare it with your own schedule, and finally confirm the appointment time.", "is_hot": "0", "copyright": "0", "important_words": "reception\r\nappointment\r\ncheck-up\r\nearliest\r\navailable\r\nschedule\r\nconfirm\r\ndate\r\ncalendar\r\ndelay\r\nreceptionist\r\nconsult\r\nslot\r\nconvenient\r\ninconvenient\r\nreminder\r\nreschedule\r\ncancel\r\npostpone\r\nadvance\r\nprior\r\nnotice\r\nconflict\r\narrange\r\naccommodate\r\npreference\r\navailability\r\nprocedure\r\ninformation\r\ninquiry\r\nassistance", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "189", "category_id": "74", "title": "描述症状", "sub_title": "正确描述自己的状况", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-27/6564516347c70.png", "user_text": "Say 'Hello, how are you feeling today?'", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你感到身体不适，决定去看医生。你详细描述了身体的不适感和症状持续的时间。同时，你积极询问医生的专业意见和治疗建议，期待能尽快恢复健康。 ", "info_en": "You feel unwell and decide to see a doctor. You describe in detail the discomfort and the duration of the symptoms. At the same time, you actively ask the doctor for professional advice and treatment suggestions, hoping to recover as soon as possible.", "is_hot": "0", "copyright": "0", "important_words": "doctor\r\nnurse\r\npatient\r\nmedicine\r\npharmacy\r\nprescription\r\nappointment\r\nexamination\r\ntreatment\r\nsurgery\r\nemergency\r\nclinic\r\npain\r\ndisease\r\nhealth\r\nsymptom\r\ndiagnosis\r\nfever\r\ncough\r\ncold\r\nflu\r\ninfection\r\nallergy\r\ninjury\r\nfracture\r\nx-ray\r\nblood test\r\ninjection\r\nvaccine\r\nbed rest\r\nrecovery\r\ndischarge\r\ninsurance\r\nmedical history\r\nphysical examination\r\noperation\r\nward\r\nspecialist\r\nfirst aid\r\nbandage\r\nplaster\r\nstethoscope\r\nthermometer\r\nblood pressure\r\nheartbeat\r\nbreathing\r\nheadache\r\nstomachache\r\ndizziness\r\nadvice", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}, {"id": "192", "category_id": "74", "title": "治疗咨询", "sub_title": "就医检查咨询", "tag": "", "tag_color": "", "tag_bg_color": "", "tag_bg_color2": "", "pic": "https://peiyinimg.qupeiyin.cn/2023-11-28/65657fb95cf05.png", "user_text": "You should start the conversation with simple greetings. Then say 'Let's go over your symptoms.'", "user_continue_text": "I'm distracted just now. Directly ask me a similar question with your last message based on what we talked about. Avoid saying any affirmations such as 'sure', 'sure, no problem', 'definitely', 'of course', or 'certainly', or 'no problem'; get right to the question.", "theme_type": "2", "url": "", "tyid": "0", "info_cn": "你来到医院寻求医生的帮助，你可以根据你的症状向医生咨询治疗方案。你可以询问是否需要进行相关检查，以及检查结果何时能够得知。同时，你还可以询问医生是否需要进行复诊，以便进行进一步的治疗和观察。", "info_en": "You came to the hospital seeking the help of a doctor. Based on your symptoms, you can consult the doctor about treatment options. You can also ask if any relevant tests are needed and when you can get the test results. At the same time, you can ask the doctor if you need to come back for a follow-up visit for further treatment and observation.", "is_hot": "0", "copyright": "0", "important_words": "hospital\r\ndoctor\r\nnurse\r\npatient\r\nmedicine\r\npharmacy\r\nprescription\r\nappointment\r\nexamination\r\ntreatment\r\nsurgery\r\nemergency\r\nclinic\r\npain\r\ndisease\r\nhealth\r\nsymptom\r\ndiagnosis\r\nfever\r\ncough\r\ncold\r\nflu\r\ninfection\r\nallergy\r\ninjury\r\nfracture\r\nx-ray\r\nblood test\r\ninjection\r\nvaccine\r\nbed rest\r\nrecovery\r\ndischarge\r\ninsurance\r\nmedical history\r\nphysical examination\r\noperation\r\nward\r\nspecialist\r\nfirst aid\r\nbandage\r\nplaster\r\nstethoscope\r\nthermometer\r\nblood pressure\r\nheartbeat\r\nbreath\r\nheadache\r\nstomachache\r\ndizziness", "min_version": "", "max_version": "", "hot_sort": "0", "sort": "0", "jump_task": "0", "begin_guide": []}]}