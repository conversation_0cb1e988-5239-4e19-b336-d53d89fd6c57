"""add_school_and_grade_to_account

Revision ID: 93bb70e1bdec
Revises: bdd1d23a9c59
Create Date: 2025-07-06 23:17:27.445890

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '93bb70e1bdec'
down_revision: Union[str, None] = 'bdd1d23a9c59'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account', sa.Column('school', sa.String(length=200), nullable=True, comment='学校名称'))
    op.add_column('account', sa.Column('grade', sa.String(length=50), nullable=True, comment='年级'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account', 'grade')
    op.drop_column('account', 'school')
    # ### end Alembic commands ###
