"""remove_school_name_from_classes

Revision ID: e459a5df898f
Revises: 66d3a3e2f288
Create Date: 2025-07-06 23:28:32.171921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e459a5df898f'
down_revision: Union[str, None] = '66d3a3e2f288'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_classes_school_name', table_name='classes')
    op.drop_column('classes', 'school_name')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('classes', sa.Column('school_name', mysql.VARCHAR(length=200), nullable=True, comment='学校名称'))
    op.create_index('ix_classes_school_name', 'classes', ['school_name'], unique=False)
    # ### end Alembic commands ###
