"""refactor_school_grade_fields

Revision ID: 66d3a3e2f288
Revises: 93bb70e1bdec
Create Date: 2025-07-06 23:20:01.168308

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '66d3a3e2f288'
down_revision: Union[str, None] = '93bb70e1bdec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account', 'school')
    op.drop_column('account', 'grade')
    op.add_column('classes', sa.Column('school_name', sa.String(length=200), nullable=True, comment='学校名称'))
    op.drop_index('ix_classes_school_id', table_name='classes')
    op.create_index(op.f('ix_classes_school_name'), 'classes', ['school_name'], unique=False)
    op.drop_column('classes', 'school_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('classes', sa.Column('school_id', mysql.VARCHAR(length=80), nullable=True, comment='学校ID'))
    op.drop_index(op.f('ix_classes_school_name'), table_name='classes')
    op.create_index('ix_classes_school_id', 'classes', ['school_id'], unique=False)
    op.drop_column('classes', 'school_name')
    op.add_column('account', sa.Column('grade', mysql.VARCHAR(length=50), nullable=True, comment='年级'))
    op.add_column('account', sa.Column('school', mysql.VARCHAR(length=200), nullable=True, comment='学校名称'))
    # ### end Alembic commands ###
