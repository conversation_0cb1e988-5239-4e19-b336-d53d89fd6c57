"""add_school_name_back_as_optional

Revision ID: 3bbe22c35a31
Revises: e459a5df898f
Create Date: 2025-07-06 23:33:24.293817

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3bbe22c35a31'
down_revision: Union[str, None] = 'e459a5df898f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('classes', sa.Column('school_name', sa.String(length=200), nullable=True, comment='学校名称(可选)'))
    op.create_index('ix_classes_school_name', 'classes', ['school_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_classes_school_name', table_name='classes')
    op.drop_column('classes', 'school_name')
    # ### end Alembic commands ###
