"""fix_class_id_data_types

Revision ID: bdd1d23a9c59
Revises: db5cd8099690
Create Date: 2025-07-06 22:59:41.562749

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'bdd1d23a9c59'
down_revision: Union[str, None] = 'db5cd8099690'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('class_students', 'class_id',
               existing_type=mysql.VARCHAR(length=80),
               type_=sa.Integer(),
               existing_comment='班级ID',
               existing_nullable=False)
    op.alter_column('class_teachers', 'class_id',
               existing_type=mysql.VARCHAR(length=80),
               type_=sa.Integer(),
               existing_comment='班级ID',
               existing_nullable=False)
    op.alter_column('tasks', 'class_id',
               existing_type=mysql.VARCHAR(length=80),
               type_=sa.Integer(),
               existing_comment='班级ID',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tasks', 'class_id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=80),
               existing_comment='班级ID',
               existing_nullable=False)
    op.alter_column('class_teachers', 'class_id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=80),
               existing_comment='班级ID',
               existing_nullable=False)
    op.alter_column('class_students', 'class_id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=80),
               existing_comment='班级ID',
               existing_nullable=False)
    # ### end Alembic commands ###
