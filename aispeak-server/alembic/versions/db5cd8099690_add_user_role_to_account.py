"""add_user_role_to_account

Revision ID: db5cd8099690
Revises: 2a9628bab4ee
Create Date: 2025-07-06 22:54:49.637833

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'db5cd8099690'
down_revision: Union[str, None] = '2a9628bab4ee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('class_teachers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('class_id', sa.String(length=80), nullable=False, comment='班级ID'),
    sa.Column('teacher_id', sa.String(length=80), nullable=False, comment='教师ID'),
    sa.Column('join_date', sa.DateTime(), nullable=False, comment='加入日期'),
    sa.Column('leave_date', sa.DateTime(), nullable=True, comment='离开日期'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
    sa.Column('role', sa.String(length=50), nullable=True, comment='教师角色'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_class_teachers_class_id'), 'class_teachers', ['class_id'], unique=False)
    op.create_index(op.f('ix_class_teachers_id'), 'class_teachers', ['id'], unique=False)
    op.create_index(op.f('ix_class_teachers_status'), 'class_teachers', ['status'], unique=False)
    op.create_index(op.f('ix_class_teachers_teacher_id'), 'class_teachers', ['teacher_id'], unique=False)
    op.create_index(op.f('ix_tasks_class_id'), 'tasks', ['class_id'], unique=False)
    op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)
    op.create_index(op.f('ix_tasks_lesson_id'), 'tasks', ['lesson_id'], unique=False)
    op.create_index(op.f('ix_tasks_subject'), 'tasks', ['subject'], unique=False)
    op.create_index(op.f('ix_tasks_teacher_id'), 'tasks', ['teacher_id'], unique=False)
    op.create_index(op.f('ix_tasks_textbook_id'), 'tasks', ['textbook_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tasks_textbook_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_teacher_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_subject'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_lesson_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_class_id'), table_name='tasks')
    op.drop_index(op.f('ix_class_teachers_teacher_id'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_status'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_id'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_class_id'), table_name='class_teachers')
    op.drop_table('class_teachers')
    # ### end Alembic commands ###
