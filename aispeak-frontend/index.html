<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="AI-Speak, 英语学习, AI聊天, 外语学习, 语音聊天, 多语言AI, 口语练习, 发音纠正"/>
    <meta name="description" content="AI-Speak —— 您的智能语言学习助手。通过AI技术，提供多语言支持和个性化学习体验，帮助您提升英语口语能力。实时对话、发音纠正、实用会话模拟，助您轻松突破语言障碍，自信交流。"/>
    <meta property="og:title" content="AI-Speak">
    <title>AI-Speak | 智能语言学习助手</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <link rel="icon" type="image/x-icon" href="/favicon.png">
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app">
      <!--app-html-->
      <!-- 移动到应用容器内部 -->
      <!-- <div class="beian-footer">
        <a href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2025092232号</a>
      </div> -->
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>