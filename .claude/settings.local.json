{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pip3 list:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(python -m alembic revision:*)", "<PERSON><PERSON>(source:*)", "Bash(alembic revision:*)", "Bash(alembic upgrade:*)", "Bash(python -m pytest tests/test-classes-python.sh -v)", "<PERSON><PERSON>(chmod:*)", "Bash(python scripts/update_class_codes.py:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python:*)"], "deny": []}}